<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美妆销售分析系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it/dist/markdown-it.min.js"></script>
    <style>
        body {
            font-family: 'Roboto', 'Microsoft YaHei', Arial, sans-serif;
            background: #faf7fb;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 0;
            gap: 32px;
        }
        .sidebar {
            width: 320px;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(255, 0, 128, 0.08);
            padding: 32px 24px 24px 24px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        .sidebar h2 {
            color: #e91e63;
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 18px;
        }
        .form-group {
            margin-bottom: 18px;
        }
        .form-group label {
            display: block;
            font-size: 15px;
            color: #e91e63;
            margin-bottom: 6px;
        }
        .form-group input[type="text"],
        .form-group textarea {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #f8bbd0;
            border-radius: 6px;
            background: #fff0f6;
            font-size: 15px;
            color: #333;
            margin-bottom: 6px;
        }
        .form-group input[type="file"] {
            margin-bottom: 6px;
        }
        .upload-btn {
            background: linear-gradient(90deg, #f06292 0%, #e040fb 100%);
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 10px 0;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            margin-bottom: 10px;
        }
        .upload-btn:disabled {
            background: #f8bbd0;
            color: #fff;
            cursor: not-allowed;
        }
        .data-source-list {
            margin-top: 10px;
        }
        .data-source-list-title {
            color: #e91e63;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        .data-source-empty {
            color: #aaa;
            font-size: 14px;
            padding: 8px 0;
            background: #fce4ec;
            border-radius: 6px;
            text-align: center;
        }
        .main {
            flex: 1;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(255, 0, 128, 0.08);
            padding: 32px 36px 24px 36px;
            display: flex;
            flex-direction: column;
            min-width: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 18px;
        }
        .header h1 {
            color: #e91e63;
            font-size: 32px;
            font-weight: 900;
            margin: 0 0 8px 0;
            letter-spacing: 2px;
        }
        .header p {
            color: #888;
            font-size: 16px;
            margin: 0;
        }
        .chat-area {
            flex: 1;
            overflow-y: auto;
            background: #f8bbd0;
            background: linear-gradient(90deg, #fff0f6 0%, #fce4ec 100%);
            border-radius: 12px;
            padding: 24px 18px 18px 18px;
            margin-bottom: 18px;
            min-height: 400px;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        .input-area input[type="text"] {
            flex: 1;
            padding: 12px 14px;
            border: 1px solid #f8bbd0;
            border-radius: 6px;
            font-size: 16px;
            background: #fff0f6;
        }
        .input-area button {
            background: linear-gradient(90deg, #f06292 0%, #e040fb 100%);
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 0 28px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
        }
        .input-area button:disabled {
            background: #f8bbd0;
            color: #fff;
            cursor: not-allowed;
        }
        /* 消息块样式后续补充 */
    </style>
</head>
<body>
    <div class="header">
        <h1>美妆销售数据分析助手</h1>
        <p>让业务人员轻松获取数据洞察，辅助决策</p>
    </div>
    <div class="container">
        <div class="sidebar">
            <h2>数据源管理</h2>
            <div class="form-group">
                <label>上传数据文件</label>
                <input type="file" disabled>
            </div>
            <div class="form-group">
                <label>数据集名称</label>
                <input type="text" placeholder="如：2025年第一季度销售数据" disabled>
            </div>
            <div class="form-group">
                <label>数据集描述（可选）</label>
                <textarea rows="3" placeholder="数据集的详细描述..." disabled></textarea>
            </div>
            <button class="upload-btn" disabled>上传数据</button>
            <div class="data-source-list">
                <div class="data-source-list-title">可用数据源</div>
                <div class="data-source-empty">没有可用的数据源</div>
            </div>
        </div>
        <div class="main">
            <div class="chat-area" id="messageContainer"></div>
            <div class="input-area">
                <input type="text" id="taskInput" placeholder="请输入您的问题...">
                <button id="sendButton">发送</button>
            </div>
        </div>
    </div>
    <script>
    // 专家中英文映射
    const expertMap = {
        planning_expert: "任务规划专家",
        query_analysis_expert: "数据分析问题解析专家",
        sql_generator_expert: "SQL语句生成专家",
        sql_executor_expert: "SQL语句执行专家",
        visualization_expert: "数据可视化专家",
        data_analysis_expert: "数据分析专家",
        knowledge_retrieval_expert: "知识检索专家"
    };
    // 特殊头部映射
    const specialHeaderMap = {
        planning_expert: {
            TextMessage: "分析计划"
        },
        data_analysis_expert: {
            TextMessage: "分析结果"
        },
        visualization_expert: {
            TextMessage: "可视化结果",
            Message: "可视化结果"
        }
    };
    const messageContainer = document.getElementById('messageContainer');
    const taskInput = document.getElementById('taskInput');
    const sendButton = document.getElementById('sendButton');
    const md = window.markdownit();
    let ws = null;
    let reconnectAttempts = 0;
    const MAX_RECONNECT_ATTEMPTS = 5;
    let heartbeatInterval = null;
    let streamingBlocks = {}; // {source: {div, content, type, collapsed}}

    // 工具函数
    function scrollToBottom() {
        messageContainer.scrollTop = messageContainer.scrollHeight;
    }
    function createMessageBlock(headerText, type, source) {
        const block = document.createElement('div');
        block.className = 'msg-block';
        block.style.marginBottom = '18px';
        // header
        const header = document.createElement('div');
        header.className = 'msg-header';
        header.textContent = headerText;
        header.style.fontWeight = 'bold';
        header.style.fontSize = '17px';
        header.style.color = '#e91e63';
        header.style.marginBottom = '6px';
        block.appendChild(header);
        // content
        const content = document.createElement('div');
        content.className = 'msg-content';
        content.style.background = '#fff0f6';
        content.style.borderRadius = '8px';
        content.style.padding = '12px 16px';
        content.style.fontSize = '15px';
        content.style.color = '#333';
        content.style.wordBreak = 'break-all';
        block.appendChild(content);
        // 折叠按钮
        if (type === 'ModelClientStreamingChunkEvent') {
            const collapseBtn = document.createElement('button');
            collapseBtn.textContent = '折叠';
            collapseBtn.style.marginLeft = '12px';
            collapseBtn.style.background = '#f8bbd0';
            collapseBtn.style.color = '#e91e63';
            collapseBtn.style.border = 'none';
            collapseBtn.style.borderRadius = '4px';
            collapseBtn.style.cursor = 'pointer';
            collapseBtn.style.fontSize = '13px';
            collapseBtn.style.padding = '2px 10px';
            collapseBtn.onclick = () => {
                content.style.display = content.style.display === 'none' ? '' : 'none';
                collapseBtn.textContent = content.style.display === 'none' ? '展开' : '折叠';
            };
            header.appendChild(collapseBtn);
        }
        messageContainer.appendChild(block);
        scrollToBottom();
        return {block, header, content};
    }
    function renderMarkdown(content, container) {
        container.innerHTML = md.render(content);
    }
    function renderError(msg) {
        const div = document.createElement('div');
        div.style.color = '#f14c4c';
        div.style.margin = '12px 0';
        div.textContent = msg;
        messageContainer.appendChild(div);
        scrollToBottom();
    }
    // 渲染 ECharts 图表
    function renderECharts(jsonStr, container) {
        let json;
        try {
            json = JSON.parse(jsonStr);
        } catch {
            container.innerHTML = '<span style="color:#f14c4c">可视化配置解析失败</span>';
            return;
        }
        // 创建图表容器
        const chartDiv = document.createElement('div');
        chartDiv.style.width = '100%';
        chartDiv.style.height = '360px';
        container.innerHTML = '';
        container.appendChild(chartDiv);
        // 生成option
        let option = {};
        if (json.type === 'pie') {
            option = {
                title: {text: json.config.title, left: 'center'},
                tooltip: {trigger: 'item'},
                legend: {orient: 'vertical', left: 'left'},
                series: [{
                    name: json.config.seriesName,
                    type: 'pie',
                    radius: '60%',
                    data: json.data || [],
                    emphasis: {
                        itemStyle: {shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)'}
                    }
                }]
            };
        } else if (json.type === 'table') {
            // 渲染表格
            let html = `<div style='overflow-x:auto'><table style='width:100%;border-collapse:collapse;'>`;
            html += `<tr>`;
            for (const col of json.config.columns) {
                html += `<th style='border:1px solid #e91e63;padding:6px 8px;background:#fce4ec;'>${col}</th>`;
            }
            html += `</tr>`;
            if (json.data) {
                for (const row of json.data) {
                    html += `<tr>`;
                    for (const col of json.config.columns) {
                        html += `<td style='border:1px solid #f8bbd0;padding:6px 8px;'>${row[col] ?? ''}</td>`;
                    }
                    html += `</tr>`;
                }
            }
            html += `</table></div>`;
            container.innerHTML = html;
            return;
        } else {
            // 其他类型，简单渲染option
            option = json.config || {};
        }
        const chart = echarts.init(chartDiv);
        chart.setOption(option);
    }
    // 解析content中的json
    function extractJsonFromContent(content) {
        const match = content.match(/```json[\s\S]*?({[\s\S]*?})[\s\S]*?```/);
        if (match) return match[1];
        // 兼容没有markdown包裹的情况
        const match2 = content.match(/({[\s\S]*})/);
        if (match2) return match2[1];
        return null;
    }
    // WebSocket 相关
    function startHeartbeat() {
        if (heartbeatInterval) clearInterval(heartbeatInterval);
        heartbeatInterval = setInterval(() => {
            if (ws && ws.readyState === WebSocket.OPEN) ws.send('ping');
        }, 30000);
    }
    function stopHeartbeat() {
        if (heartbeatInterval) clearInterval(heartbeatInterval);
        heartbeatInterval = null;
    }
    function connectWebSocket() {
        if (ws) ws.close();
        ws = new WebSocket('ws://localhost:8000/ws');
        ws.onopen = () => {
            sendButton.disabled = false;
            reconnectAttempts = 0;
            startHeartbeat();
        };
        ws.onmessage = (event) => {
            let data;
            try {
                data = JSON.parse(event.data);
            } catch {
                return;
            }
            if (data === 'pong' || data === 'heartbeat') return;
            if (data.type === 'Error') {
                renderError(data.content);
                return;
            }
            // 处理流式消息
            if (data.type === 'ModelClientStreamingChunkEvent') {
                // 头部
                let headerText = expertMap[data.source] || data.source;
                if (specialHeaderMap[data.source] && specialHeaderMap[data.source][data.type]) {
                    headerText = specialHeaderMap[data.source][data.type];
                }
                // 若已存在流式块，追加，否则新建
                if (!streamingBlocks[data.source]) {
                    streamingBlocks[data.source] = createMessageBlock(headerText, data.type, data.source);
                    streamingBlocks[data.source].content.textContent = '';
                    streamingBlocks[data.source].type = data.type;
                    streamingBlocks[data.source].collapsed = false;
                }
                streamingBlocks[data.source].content.textContent += data.content;
                scrollToBottom();
            } else {
                // 非流式消息，自动折叠流式块
                if (streamingBlocks[data.source]) {
                    streamingBlocks[data.source].content.style.display = 'none';
                    const btn = streamingBlocks[data.source].header.querySelector('button');
                    if (btn) btn.textContent = '展开';
                    streamingBlocks[data.source].collapsed = true;
                }
                // 头部
                let headerText = expertMap[data.source] || data.source;
                if (specialHeaderMap[data.source] && specialHeaderMap[data.source][data.type]) {
                    headerText = specialHeaderMap[data.source][data.type];
                }
                // planning_expert的TextMessage用"分析计划"，data_analysis_expert的TextMessage用"分析结果"，visualization_expert的TextMessage/Message用"可视化结果"
                const {block, content} = createMessageBlock(headerText, data.type, data.source);
                // 渲染内容
                if (data.source === 'visualization_expert' && (data.type === 'TextMessage' || data.type === 'Message')) {
                    // 渲染图表
                    const jsonStr = extractJsonFromContent(data.content);
                    if (jsonStr) {
                        renderECharts(jsonStr, content);
                    } else {
                        renderMarkdown(data.content, content);
                    }
                } else {
                    renderMarkdown(data.content, content);
                }
                // 清理流式块
                streamingBlocks[data.source] = null;
            }
        };
        ws.onclose = () => {
            sendButton.disabled = true;
            stopHeartbeat();
            if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                reconnectAttempts++;
                setTimeout(connectWebSocket, 1000 * reconnectAttempts);
            } else {
                renderError('连接已断开，请刷新页面重试');
            }
        };
        ws.onerror = (error) => {
            renderError('连接发生错误，请刷新页面重试');
        };
    }
    sendButton.addEventListener('click', () => {
        const task = taskInput.value.trim();
        if (task && ws && ws.readyState === WebSocket.OPEN) {
            // 用户消息
            const {block, content} = createMessageBlock('用户', 'TextMessage', 'user');
            renderMarkdown(task, content);
            ws.send(task);
            taskInput.value = '';
            sendButton.disabled = true;
        }
    });
    taskInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') sendButton.click();
    });
    // 初始连接
    connectWebSocket();
    </script>
</body>
</html> 