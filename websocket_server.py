import asyncio
import json
import os
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any
import uvicorn
from file_upload_handler import file_handler
from beauty_sales_analysis_system import team as loreal_analysis_team
from data_analysis_system import analysis_system as custom_file_analysis_system

app = FastAPI()

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 存储WebSocket连接
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.heartbeat_tasks: Dict[WebSocket, asyncio.Task] = {}

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        # 为每个连接创建心跳任务
        self.heartbeat_tasks[websocket] = asyncio.create_task(self.heartbeat(websocket))

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        # 取消心跳任务
        if websocket in self.heartbeat_tasks:
            self.heartbeat_tasks[websocket].cancel()
            del self.heartbeat_tasks[websocket]

    async def send_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception:
            # 如果发送失败，断开连接
            self.disconnect(websocket)

    async def heartbeat(self, websocket: WebSocket):
        """发送心跳包以保持连接活跃"""
        try:
            while True:
                await asyncio.sleep(30)  # 每30秒发送一次心跳
                try:
                    await websocket.send_text(json.dumps({"type": "heartbeat"}))
                except Exception:
                    # 如果发送心跳失败，断开连接
                    self.disconnect(websocket)
                    break
        except asyncio.CancelledError:
            # 心跳任务被取消，正常退出
            pass

manager = ConnectionManager()

def get_analysis_team():
    """根据当前数据源获取对应的分析团队"""
    current_source = file_handler.get_current_data_source()
    if current_source == "loreal":
        # 使用欧莱雅销售数据分析系统（PostgreSQL）
        return loreal_analysis_team
    elif current_source == "custom_file":
        # 使用自定义文件分析系统（SQLite）
        return custom_file_analysis_system
    else:
        raise ValueError("No data source selected")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                # 处理心跳响应
                if message.get("type") == "heartbeat":
                    continue
                # 处理查询请求
                if message.get("type") == "query":
                    # 获取当前数据源
                    current_source = file_handler.get_current_data_source()
                    if not current_source:
                        await manager.send_message(
                            json.dumps({
                                "type": "error",
                                "message": "请先选择数据源"
                            }),
                            websocket
                        )
                        continue

                    # 获取对应的分析团队
                    analysis_team = get_analysis_team()
                    
                    # 运行分析
                    async for response in analysis_team.run_stream(task=message["content"]):
                        await manager.send_message(
                            json.dumps({
                                "type": "response",
                                "content": str(response)
                            }),
                            websocket
                        )

            except json.JSONDecodeError:
                await manager.send_message(
                    json.dumps({
                        "type": "error",
                        "message": "Invalid JSON format"
                    }),
                    websocket
                )

    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        # 处理其他异常
        manager.disconnect(websocket)
        print(f"WebSocket error: {str(e)}")

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    try:
        # 保存上传的文件
        content = await file.read()
        file_path = file_handler.save_uploaded_file(content, file.filename)
        
        # 处理文件到SQLite
        schema_info = file_handler.process_file_to_sqlite(file_path)
        
        return {
            "status": "success",
            "message": "File uploaded and processed successfully",
            "schema_info": schema_info
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

@app.post("/select-data-source")
async def select_data_source(data_source: str):
    try:
        if data_source == "loreal":
            file_handler.set_loreal_database()
        else:
            # 如果是自定义文件，schema_info已经在process_file_to_sqlite中设置
            pass

        return {
            "status": "success",
            "message": f"Data source set to {data_source}",
            "current_source": file_handler.get_current_data_source()
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

@app.get("/current-data-source")
async def get_current_data_source():
    return {
        "current_source": file_handler.get_current_data_source()
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000) 