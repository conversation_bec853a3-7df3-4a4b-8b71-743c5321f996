import sqlite3
import os
from typing import Dict, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class FileProcessor:
    def __init__(self, upload_dir: str = "uploads", db_path: str = "temp_data.db"):
        """初始化文件处理器
        
        Args:
            upload_dir: 文件上传目录
            db_path: SQLite数据库文件路径
        """
        self.upload_dir = upload_dir
        self.db_path = db_path
        
        # 创建上传目录
        os.makedirs(upload_dir, exist_ok=True)
        
        # 初始化SQLite数据库
        self._init_database()
    
    def _init_database(self):
        """初始化SQLite数据库"""
        try:
            # 检查数据库文件是否存在
            db_exists = os.path.exists(self.db_path)
            
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if not db_exists:
                # 创建元数据表，用于存储上传文件的信息
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS file_metadata (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_name TEXT NOT NULL,
                        original_filename TEXT NOT NULL,
                        upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        row_count INTEGER,
                        column_count INTEGER,
                        description TEXT
                    )
                ''')
                
                # 创建索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_upload_time 
                    ON file_metadata(upload_time)
                ''')
                
                conn.commit()
                logger.info("SQLite数据库初始化完成")
            else:
                logger.info("使用已存在的SQLite数据库")
            
            conn.close()
            
        except Exception as e:
            logger.error(f"初始化数据库时出错: {str(e)}")
            raise
    
    def get_available_tables(self) -> Dict[str, Any]:
        """获取可用的数据表列表
        
        Returns:
            Dict包含表信息
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 从元数据表获取表信息
            cursor.execute('''
                SELECT 
                    table_name,
                    original_filename,
                    upload_time,
                    row_count,
                    column_count,
                    description
                FROM file_metadata
                ORDER BY upload_time DESC
            ''')
            
            tables = cursor.fetchall()
            
            table_info = []
            for table in tables:
                # 获取列信息
                cursor.execute(f"PRAGMA table_info({table[0]})")
                columns = cursor.fetchall()
                
                table_info.append({
                    "name": table[0],
                    "original_filename": table[1],
                    "upload_time": table[2],
                    "row_count": table[3],
                    "column_count": table[4],
                    "description": table[5],
                    "columns": [col[1] for col in columns]
                })
            
            conn.close()
            
            return {
                "success": True,
                "tables": table_info
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def save_file(self, file, filename: str) -> Dict[str, Any]:
        """保存上传的文件
        
        Args:
            file: 文件对象
            filename: 原始文件名
            
        Returns:
            Dict包含保存结果
        """
        try:
            # 生成保存路径
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            save_path = os.path.join(self.upload_dir, f"{timestamp}_{filename}")
            
            # 保存文件
            with open(save_path, "wb") as f:
                f.write(file.read())
            
            return {
                "success": True,
                "file_path": save_path
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def process_file(self, file_path: str) -> Dict[str, Any]:
        """处理上传的文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict包含处理结果
        """
        try:
            # 这里添加文件处理逻辑
            # 例如：读取文件内容，分析数据结构等
            
            return {
                "success": True,
                "data_info": {
                    "row_count": 0,  # 示例值
                    "column_count": 0,  # 示例值
                    "columns": []  # 示例值
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def import_to_database(self, file_path: str, table_name: str) -> Dict[str, Any]:
        """将文件数据导入到数据库
        
        Args:
            file_path: 文件路径
            table_name: 目标表名
            
        Returns:
            Dict包含导入结果
        """
        try:
            # 这里添加数据导入逻辑
            # 例如：读取文件，创建表，导入数据等
            
            return {
                "success": True,
                "message": f"数据已成功导入到表 {table_name}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def cleanup_old_data(self, max_age_hours: int = 24):
        """清理旧数据
        
        Args:
            max_age_hours: 最大保留时间（小时）
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取需要清理的表
            cursor.execute('''
                SELECT table_name, upload_time
                FROM file_metadata
                WHERE datetime(upload_time) < datetime('now', ?)
            ''', (f'-{max_age_hours} hours',))
            
            tables_to_clean = cursor.fetchall()
            
            # 删除旧表
            for table in tables_to_clean:
                cursor.execute(f"DROP TABLE IF EXISTS {table[0]}")
            
            # 删除元数据
            cursor.execute('''
                DELETE FROM file_metadata
                WHERE datetime(upload_time) < datetime('now', ?)
            ''', (f'-{max_age_hours} hours',))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"清理数据时出错: {str(e)}")