.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

body {
  background: #f7f7f7;
}
.ant-card {
  border-radius: 16px;
}
.ant-btn-primary {
  background: #FF5CA2 !important;
  border: none !important;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  font-weight: bold;
  color: #FF5CA2;
}
/* 侧边栏样式 */
.ant-layout-sider {
  background: #fff !important;
  border-radius: 16px;
  box-shadow: 0 2px 8px #eee;
}
.ant-layout {
  background: #f7f7f7 !important;
}
.ant-input[disabled], .ant-input-disabled, .ant-input[disabled]:hover {
  background: #f7f7f7 !important;
  color: #bbb !important;
}
.ant-input {
  border-radius: 8px;
}
.ant-btn[disabled] {
  background: #eee !important;
  color: #bbb !important;
}
