import React, { useEffect, useRef, useState } from "react";
import { Layout, Card, Collapse, Input, Button, message as antdMessage } from "antd";
import ReactMarkdown from "react-markdown";
import { Pie, Bar } from "@ant-design/charts";
import { Table } from "antd";
import "./App.css";

const { Sider, Content } = Layout;
const { Panel } = Collapse;

const expertMap = {
  planning_expert: "任务规划专家",
  query_analysis_expert: "数据分析问题解析专家",
  sql_generator_expert: "SQL语句生成专家",
  sql_executor_expert: "SQL语句执行专家",
  visualization_expert: "数据可视化专家",
  data_analysis_expert: "数据分析专家",
  knowledge_retrieval_expert: "知识检索专家"
};

function VisualizationCard({ content }) {
  // 提取json
  const match = content.match(/```json\s*([\s\S]+?)```/);
  if (!match) return <ReactMarkdown>{content}</ReactMarkdown>;
  let config;
  try {
    config = JSON.parse(match[1]);
  } catch {
    return <ReactMarkdown>{content}</ReactMarkdown>;
  }
  if (config.type === "pie") {
    return <Pie {...config.config} />;
  }
  if (config.type === "bar") {
    return <Bar {...config.config} />;
  }
  if (config.type === "table") {
    return (
      <Table
        columns={config.config.columns.map(col => ({ title: col, dataIndex: col }))}
        dataSource={config.config.data}
        title={() => config.config.title}
        pagination={false}
        bordered
        rowKey={(row, idx) => idx}
      />
    );
  }
  return <ReactMarkdown>{content}</ReactMarkdown>;
}

function ExpertMessageList({ messages }) {
  // 记录每个专家的流式内容和折叠状态
  const [streamStates, setStreamStates] = useState({});
  const listRef = useRef();

  useEffect(() => {
    // 自动滚动到底部
    if (listRef.current) {
      listRef.current.scrollTop = listRef.current.scrollHeight;
    }
    // 自动折叠逻辑
    messages.forEach(msg => {
      if (
        (msg.type === "TextMessage" || msg.type === "Message") &&
        streamStates[msg.source]?.streaming
      ) {
        setStreamStates(prev => ({
          ...prev,
          [msg.source]: { ...prev[msg.source], streaming: false }
        }));
      }
      if (msg.type === "ModelClientStreamingChunkEvent") {
        setStreamStates(prev => ({
          ...prev,
          [msg.source]: { streaming: true }
        }));
      }
    });
    // eslint-disable-next-line
  }, [messages]);

  return (
    <div ref={listRef} style={{ maxHeight: 600, overflowY: "auto", padding: 16 }}>
      {messages.map((msg, idx) => {
        // 可视化专家
        if (msg.source === "visualization_expert") {
          if (msg.type === "ModelClientStreamingChunkEvent") {
            return (
              <Collapse key={idx} defaultActiveKey={streamStates[msg.source]?.streaming ? ["1"] : []}>
                <Panel header={expertMap[msg.source]} key="1">
                  <span>{msg.content}</span>
                </Panel>
              </Collapse>
            );
          }
          // Message/TextMessage 展示图表
          return (
            <Card key={idx} title={expertMap[msg.source]}>
              <VisualizationCard content={msg.content} />
            </Card>
          );
        }

        // 任务规划专家/数据分析专家特殊处理
        if (msg.source === "planning_expert" && (msg.type === "TextMessage" || msg.type === "Message")) {
          return (
            <Card key={idx} title="分析计划">
              <ReactMarkdown>{msg.content}</ReactMarkdown>
            </Card>
          );
        }
        if (msg.source === "data_analysis_expert" && (msg.type === "TextMessage" || msg.type === "Message")) {
          return (
            <Card key={idx} title="分析结果">
              <ReactMarkdown>{msg.content}</ReactMarkdown>
            </Card>
          );
        }

        // 其他专家
        if (msg.type === "ModelClientStreamingChunkEvent") {
          return (
            <Collapse key={idx} defaultActiveKey={streamStates[msg.source]?.streaming ? ["1"] : []}>
              <Panel header={expertMap[msg.source]} key="1">
                <span>{msg.content}</span>
              </Panel>
            </Collapse>
          );
        }
        return (
          <Card key={idx} title={expertMap[msg.source]}>
            <ReactMarkdown>{msg.content}</ReactMarkdown>
          </Card>
        );
      })}
    </div>
  );
}

export default function App() {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState("");
  const ws = useRef(null);

  useEffect(() => {
    // 请将 ws://localhost:8000/ws 替换为你的后端 WebSocket 地址
    ws.current = new window.WebSocket("ws://localhost:8000/ws");
    ws.current.onopen = () => {
      antdMessage.success("WebSocket 连接成功");
    };
    ws.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        setMessages((prev) => [...prev, data]);
      } catch {
        // 处理心跳等非json消息
      }
    };
    ws.current.onclose = () => {
      antdMessage.warning("WebSocket 连接关闭");
    };
    return () => {
      ws.current && ws.current.close();
    };
  }, []);

  const handleSend = () => {
    if (input.trim() && ws.current && ws.current.readyState === 1) {
      ws.current.send(input.trim());
      setInput("");
    }
  };

  return (
    <Layout style={{ minHeight: "100vh", background: "#f7f7f7" }}>
      <Sider width={340} style={{ background: "#fff", padding: 24 }}>
        <h2 style={{ color: "#FF5CA2", fontWeight: "bold" }}>数据源管理</h2>
        <div style={{ marginBottom: 24 }}>
          <Input disabled placeholder="上传数据文件" style={{ marginBottom: 8 }} />
          <Input disabled placeholder="数据集名称" style={{ marginBottom: 8 }} />
          <Input.TextArea disabled placeholder="数据集描述（可选）" style={{ marginBottom: 8 }} />
          <Button disabled type="primary" style={{ background: "#FF5CA2", border: "none" }}>上传数据</Button>
        </div>
        <div style={{ color: "#FF5CA2", fontWeight: "bold", marginBottom: 8 }}>可用数据源</div>
        <Input disabled value="没有可用的数据源" />
      </Sider>
      <Layout>
        <Content style={{ padding: 24 }}>
          <Card style={{ minHeight: 700 }}>
            <ExpertMessageList messages={messages} />
            <div style={{ display: "flex", marginTop: 16 }}>
              <Input.TextArea
                value={input}
                onChange={e => setInput(e.target.value)}
                placeholder="请输入您的问题..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                onPressEnter={e => {
                  if (!e.shiftKey) {
                    e.preventDefault();
                    handleSend();
                  }
                }}
              />
              <Button
                type="primary"
                style={{ marginLeft: 8, background: "#FF5CA2", border: "none" }}
                onClick={handleSend}
              >
                发送
              </Button>
            </div>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
}
